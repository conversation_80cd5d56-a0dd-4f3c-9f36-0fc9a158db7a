/* eslint-disable no-restricted-syntax */

/* Возвращает только верхний уровень родителей */
/**
 * @param parents Массив элементов
 * @returns Массив родителей верхнего уровня
 */
export const filterTopLevelParents = <T extends { key: string | number }>(
  parents: T[],
): T[] => {
  // Сортируем по длине ключа (в целях эффективного поиска)
  const sortedParents = [...parents].sort(
    (a, b) => String(a.key).length - String(b.key).length,
  );

  const topLevelParents: T[] = [];
  const allKeys = new Set<string>();

  // Проходим по каждому родителю
  for (const parent of sortedParents) {
    const parentKey = String(parent.key);

    // Проверяем, является ли текущий родитель потомком уже добавленного родителя
    let isDescendant = false;
    for (const key of allKeys) {
      if (parentKey.startsWith(`${key}-`)) {
        isDescendant = true;
        break;
      }
    }

    if (!isDescendant) {
      topLevelParents.push(parent);
      allKeys.add(parentKey);
    }
  }

  return topLevelParents;
};
