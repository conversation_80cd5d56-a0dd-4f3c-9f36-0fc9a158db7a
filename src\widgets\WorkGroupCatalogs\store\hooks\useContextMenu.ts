import { notification } from 'antd';
import { useMemo } from 'react';
import { WorkGroupCatalogsStore } from 'widgets/WorkGroupCatalogs';
import { createInputsModal, PopupInput } from 'features/InputsModal';
import { createMarkNodeTree } from 'features/MarkNodeTree';
import { apiUrls } from 'shared/api';
import {
  appErrorNotification,
  generateUrlWithQueryParams,
  treeParserByConfig,
} from 'shared/lib';
import {
  createConfirmModal,
  TreeSelect,
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';

const renderInputs = (
  defaultValue = '',
): NonEmptyArray<PopupInput<'title'>> => [
  {
    title: 'Название каталога',
    type: 'text',
    key: 'title',
    placeholder: 'Введите название каталога',
    defaultValue,
  },
];

export const useContextMenu = (
  treeValues: TreeSelect,
  handleReset: Callback,
  user: string,
): ContextMenuItem[] => {
  const dispatch = useAppDispatch();
  const catalogs = useAppSelector(
    WorkGroupCatalogsStore.selectors.catalogsSelector,
  );
  const { handleUpdateTree } = useCreateSliceActions(
    WorkGroupCatalogsStore.reducers.slice.actions,
  );

  const parsedNodeTitle = useMemo(
    () => (treeValues?.selectedNode?.rawTitle as string)?.trim(),
    [treeValues.selectedNode],
  );

  return [
    {
      label: 'Добавить каталог',
      key: '1',
      onClick: () =>
        createInputsModal({
          shouldCloseOnBackdropClick: false,
          hideCloseButton: true,
          endpoint: generateUrlWithQueryParams(
            apiUrls.workGroupControl.catalogs.addTreeNode,
            { user },
          ),
          inputs: renderInputs(),
          bodyParser: (inputValues) => {
            if (
              treeValues.childrenTitles.includes(
                String(inputValues.title).toLowerCase().trim(),
              )
            ) {
              inputValues.isError = true;
              inputValues.errorText =
                'Каталог с таким именем уже существует, укажите другое имя.';
            }

            if (String(inputValues.title).trim() === '') {
              inputValues.isError = true;
              inputValues.errorText = 'Укажите имя каталога';
            }

            return inputValues;
          },
          onSubmit: async () => {
            handleReset();
          },
          onSuccessSubmit: () => {
            notification.success({
              message: 'Узел успешно добавлен',
            });
          },
          title: `Добавление нового узла для ${treeValues.selectedNode.title}`,
          additionalBodyKeys: {
            parentId: treeValues.selectedNode.itemId,
            headerId: catalogs.history.selectedValue,
          },
          responseBody: (res: { treeData: TreeElement[] }) =>
            handleUpdateTree(
              treeParserByConfig(res.treeData, (node) => ({
                ...node,
                disabled: false,
                isDisabled: node.disabled,
              })),
            ),
        }),
    },
    {
      label: treeValues.selectedNode.isDisabled ? '' : 'Переименовать каталог',
      key: '2',
      onClick: () =>
        createInputsModal({
          shouldCloseOnBackdropClick: false,
          hideCloseButton: true,
          endpoint: generateUrlWithQueryParams(
            apiUrls.workGroupControl.catalogs.updateTreeNode,
            { user },
          ),
          inputs: renderInputs(parsedNodeTitle),
          onSubmit: async () => {
            handleReset();
          },
          onSuccessSubmit: () => {
            notification.success({
              message: 'Название каталога успешно изменено',
            });
          },
          title: 'Переименовать каталог',
          additionalBodyKeys: {
            parentId: treeValues.selectedNode.itemId,
            headerId: catalogs.history.selectedValue,
          },
          bodyParser: (inputValues) => {
            if (
              parsedNodeTitle !== '' &&
              treeValues.siblingTitles.includes(
                String(inputValues.title).toLowerCase().trim(),
              )
            ) {
              inputValues.isError = true;
              inputValues.errorText =
                'Каталог с таким именем уже существует, либо введено текущее имя каталога. Укажите другое имя';
            }
            return inputValues;
          },
          responseBody: (res: { treeData: TreeElement[] }) =>
            handleUpdateTree(
              treeParserByConfig(res.treeData, (node) => ({
                ...node,
                disabled: false,
                isDisabled: node.disabled,
              })),
            ),
        }),
    },
    {
      label: treeValues.selectedNode.isDisabled ? '' : 'Удалить каталог',
      key: '3',
      onClick: () =>
        createConfirmModal({
          title: 'Внимание',
          message: `Вы действительно хотите удалить "${parsedNodeTitle}" ?`,
          onConfirm: async () => {
            await dispatch(
              WorkGroupCatalogsStore.actions.deleteCatalogThunk(
                generateUrlWithQueryParams(
                  apiUrls.workGroupControl.catalogs.deleteTreeNode,
                  {
                    headerId: catalogs.history.selectedValue,
                    user,
                    itemId: treeValues.selectedNode.itemId,
                  },
                ),
              ),
            )
              .unwrap()
              .then(() => {
                handleReset();
                notification.success({
                  message: `Каталог "${parsedNodeTitle}" успешно удален`,
                });
              })
              .catch((err) => {
                if (err.response && err.response?.data.isBusinessLogicError) {
                  notification.warn({ message: err.response.data.error });
                } else {
                  appErrorNotification(
                    'Произошла ошибка удаления каталога',
                    err,
                  );
                }
              });
          },
        }),
    },
    {
      label: treeValues.selectedNode.isDisabled ? '' : 'Маркировка',
      key: '4',
      onClick: () =>
        createMarkNodeTree({
          title: `Маркировка "${parsedNodeTitle}"`,
          markArray: catalogs.mark.markArray,
          onSave: async (value) => {
            await dispatch(
              WorkGroupCatalogsStore.actions.postDataThunk({
                url: generateUrlWithQueryParams(
                  apiUrls.workGroupControl.catalogs.markTreeNode,
                  { user },
                ),
                body: {
                  headerId: catalogs.history.selectedValue,
                  mark: value,
                  parentId: treeValues.selectedNode.itemId,
                },
              }),
            )
              .unwrap()
              .then(() => {
                notification.success({
                  message:
                    value === ''
                      ? 'Маркировка успешно снята'
                      : `Каталог "${parsedNodeTitle}" успешно промаркирован`,
                });
                handleReset();
              });
          },
          treeNode: treeValues.selectedNode,
        }),
    },
  ].filter((item) => item.label !== '');
};
