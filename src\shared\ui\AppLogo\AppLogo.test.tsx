import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';

import { FullScreenLogo, SideBarLogo } from '.';

describe('AppLogos', () => {
  afterEach(cleanup);

  it('FullScreenLogo должен быть в DOM', () => {
    const { container, getByTestId } = render(
      <FullScreenLogo data-testid="svg" />,
    );

    expect(container.firstChild).toBeInTheDocument();
    expect(getByTestId('svg').localName).toBe('svg');
  });

  it('SideBarLogo должен быть в DOM', () => {
    const { container, getByTestId } = render(
      <SideBarLogo data-testid="svg" />,
    );

    expect(container.firstChild).toBeInTheDocument();
    expect(getByTestId('svg').localName).toBe('svg');
  });
});
