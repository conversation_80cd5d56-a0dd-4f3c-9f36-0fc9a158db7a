const SingleSpaAppcracoPlugin = require('craco-plugin-single-spa-application');
const dotenvCra = require('dotenv-cra');
dotenvCra.config();

const singleSpaAppPlugin = {
  plugin: SingleSpaAppcracoPlugin,
  options: {
    orgName: 'lanit',
    projectName: 'kzid',
    entry: 'src/kzid.tsx',
    orgPackagesAsExternal: false, // defaults to false. marks packages that has @my-org prefix as external so they are not included in the bundle
    reactPackagesAsExternal: true, // defaults to true. marks react and react-dom as external so they are not included in the bundle
    externals: ['react-router', 'react-router-dom'], // defaults to []. marks the specified modules as external so they are not included in the bundle
    minimize: false, // defaults to false, sets optimization.minimize value
    // outputFilename: "lanit-kzid.js" // defaults to the values set for the "orgName" and "projectName" properties, in this case "my-org-my-app.js"
  },
};

// Keep any other configuration you are exporting from CRACO and add the plugin to the plugins array
module.exports = {
  process: 'process/browser',
  plugins: [
    singleSpaAppPlugin,
  ],
};
